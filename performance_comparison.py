#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能对比脚本 - 对比优化前后的性能指标
展示内存管理和轮询机制优化的效果
"""

import requests
import time
import json

LOCAL_SERVER_URL = "http://127.0.0.1:5101"

def get_performance_metrics():
    """获取当前性能指标"""
    try:
        # 获取详细统计信息
        stats_response = requests.get(f"{LOCAL_SERVER_URL}/stats", timeout=5)
        health_response = requests.get(f"{LOCAL_SERVER_URL}/health", timeout=5)
        
        if stats_response.status_code == 200 and health_response.status_code == 200:
            stats = stats_response.json()
            health = health_response.json()
            
            return {
                "timestamp": time.time(),
                "memory_management": {
                    "current_cached_tasks": stats.get("memory_management", {}).get("current_cached_tasks", 0),
                    "total_tasks_created": stats.get("memory_management", {}).get("total_tasks_created", 0),
                    "total_tasks_cleaned": stats.get("memory_management", {}).get("total_tasks_cleaned", 0),
                    "cleanup_efficiency": (
                        stats.get("memory_management", {}).get("total_tasks_cleaned", 0) / 
                        max(stats.get("memory_management", {}).get("total_tasks_created", 1), 1)
                    ) * 100
                },
                "performance": {
                    "total_requests": stats.get("performance", {}).get("total_requests", 0),
                    "success_rate": stats.get("performance_extended", {}).get("success_rate_percent", 0),
                    "average_response_time_ms": stats.get("performance_extended", {}).get("average_response_time_ms", 0),
                    "requests_per_second": stats.get("performance_extended", {}).get("requests_per_second", 0),
                    "uptime_hours": stats.get("performance_extended", {}).get("uptime_hours", 0)
                },
                "system": {
                    "memory_usage_mb": stats.get("system", {}).get("memory_usage_mb", 0),
                    "memory_usage_percent": stats.get("system", {}).get("memory_usage_percent", 0),
                    "cpu_usage_percent": stats.get("system", {}).get("cpu_usage_percent", 0),
                    "num_threads": stats.get("system", {}).get("num_threads", 0)
                },
                "queues": stats.get("queues", {}),
                "health_status": health.get("status", "unknown"),
                "warnings": health.get("warnings", [])
            }
        return None
    except Exception as e:
        print(f"❌ 获取性能指标失败: {e}")
        return None

def display_performance_summary():
    """显示性能摘要"""
    print("🔍 GeminiBridge 性能优化效果展示")
    print("=" * 70)
    
    metrics = get_performance_metrics()
    if not metrics:
        print("❌ 无法获取性能指标，请确保服务器正在运行")
        return
    
    # 健康状态
    status_emoji = "✅" if metrics["health_status"] == "healthy" else "⚠️"
    print(f"{status_emoji} 服务器状态: {metrics['health_status'].upper()}")
    
    if metrics["warnings"]:
        print("⚠️  警告:")
        for warning in metrics["warnings"]:
            print(f"   - {warning}")
    
    print("\n📊 性能指标概览:")
    print("-" * 50)
    
    # 运行时间和请求统计
    perf = metrics["performance"]
    print(f"🕐 运行时间: {perf['uptime_hours']:.1f} 小时")
    print(f"📈 总请求数: {perf['total_requests']}")
    print(f"✅ 成功率: {perf['success_rate']:.1f}%")
    print(f"⚡ 平均响应时间: {perf['average_response_time_ms']:.1f} ms")
    print(f"🚀 请求处理速度: {perf['requests_per_second']:.1f} req/s")
    
    # 内存管理效果
    print("\n💾 内存管理优化效果:")
    print("-" * 50)
    memory = metrics["memory_management"]
    print(f"📦 当前缓存任务: {memory['current_cached_tasks']}")
    print(f"🏭 总创建任务: {memory['total_tasks_created']}")
    print(f"🧹 已清理任务: {memory['total_tasks_cleaned']}")
    print(f"♻️  清理效率: {memory['cleanup_efficiency']:.1f}%")
    
    # 系统资源使用
    print("\n🖥️  系统资源使用:")
    print("-" * 50)
    system = metrics["system"]
    print(f"💾 内存使用: {system['memory_usage_mb']:.1f} MB ({system['memory_usage_percent']:.1f}%)")
    print(f"🔥 CPU使用: {system['cpu_usage_percent']:.1f}%")
    print(f"🧵 线程数: {system['num_threads']}")
    
    # 队列状态
    print("\n📋 队列状态:")
    print("-" * 50)
    queues = metrics["queues"]
    total_queued = sum(queues.values())
    print(f"📥 注入任务队列: {queues.get('injection_jobs', 0)}")
    print(f"💬 对话任务队列: {queues.get('prompt_jobs', 0)}")
    print(f"🔧 工具结果队列: {queues.get('tool_result_jobs', 0)}")
    print(f"🤖 模型获取队列: {queues.get('model_fetch_jobs', 0)}")
    print(f"📊 总排队任务: {total_queued}")
    
    # 优化效果评估
    print("\n🎯 优化效果评估:")
    print("-" * 50)
    
    # 内存管理评估
    if memory['cleanup_efficiency'] > 50:
        print("✅ 内存管理: 优秀 - 自动清理机制工作良好")
    elif memory['cleanup_efficiency'] > 20:
        print("⚠️  内存管理: 良好 - 清理机制正常工作")
    else:
        print("❌ 内存管理: 需要关注 - 清理效率较低")
    
    # 响应时间评估
    if perf['average_response_time_ms'] < 100:
        print("✅ 响应性能: 优秀 - 响应时间很快")
    elif perf['average_response_time_ms'] < 500:
        print("⚠️  响应性能: 良好 - 响应时间可接受")
    else:
        print("❌ 响应性能: 需要优化 - 响应时间较慢")
    
    # 资源使用评估
    if system['memory_usage_percent'] < 50:
        print("✅ 资源使用: 优秀 - 内存使用合理")
    elif system['memory_usage_percent'] < 80:
        print("⚠️  资源使用: 良好 - 内存使用正常")
    else:
        print("❌ 资源使用: 需要关注 - 内存使用较高")
    
    print("\n" + "=" * 70)
    
    # 优化建议
    print("💡 优化建议:")
    suggestions = []
    
    if memory['cleanup_efficiency'] < 30:
        suggestions.append("考虑调整TTL时间或清理间隔")
    
    if perf['average_response_time_ms'] > 200:
        suggestions.append("检查网络延迟或服务器负载")
    
    if system['memory_usage_percent'] > 70:
        suggestions.append("考虑增加内存或优化内存使用")
    
    if total_queued > 10:
        suggestions.append("检查任务处理速度，可能存在瓶颈")
    
    if not suggestions:
        print("🎉 系统运行良好，无需特别优化！")
    else:
        for i, suggestion in enumerate(suggestions, 1):
            print(f"{i}. {suggestion}")

def compare_with_baseline():
    """与基准性能对比"""
    print("\n📈 性能对比 (与优化前预期对比):")
    print("-" * 50)
    
    metrics = get_performance_metrics()
    if not metrics:
        return
    
    # 预期的优化效果
    expected_improvements = {
        "memory_efficiency": 70,  # 预期内存效率提升70%
        "polling_efficiency": 60,  # 预期轮询效率提升60%
        "response_time": 40       # 预期响应时间提升40%
    }
    
    print("🎯 预期优化效果:")
    print(f"   💾 内存管理效率提升: {expected_improvements['memory_efficiency']}%")
    print(f"   🔄 轮询机制效率提升: {expected_improvements['polling_efficiency']}%")
    print(f"   ⚡ 响应时间改善: {expected_improvements['response_time']}%")
    
    print("\n📊 实际表现:")
    memory_eff = metrics["memory_management"]["cleanup_efficiency"]
    response_time = metrics["performance"]["average_response_time_ms"]
    
    print(f"   💾 内存清理效率: {memory_eff:.1f}%")
    print(f"   ⚡ 平均响应时间: {response_time:.1f} ms")
    print(f"   🚀 请求处理速度: {metrics['performance']['requests_per_second']:.1f} req/s")
    
    # 简单的效果评估
    if memory_eff > 50:
        print("   ✅ 内存管理优化效果显著")
    if response_time < 200:
        print("   ✅ 响应时间优化效果良好")

def main():
    """主函数"""
    display_performance_summary()
    compare_with_baseline()
    
    print(f"\n⏰ 报告生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("💡 提示: 运行 python performance_test.py 进行详细的负载测试")

if __name__ == "__main__":
    main()
