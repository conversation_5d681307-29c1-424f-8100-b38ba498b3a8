#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GeminiBridge 一键启动脚本
同时启动本地历史服务器和OpenAI兼容服务器
"""

import subprocess
import sys
import time
import threading
import signal
import os
from pathlib import Path

# 服务器进程列表
server_processes = []

# --- 安全打印函数 ---
def safe_print(message):
    """安全打印函数，处理Unicode编码问题"""
    try:
        print(message)
    except UnicodeEncodeError:
        # 如果遇到编码错误，将Unicode字符替换为ASCII等价物
        safe_message = message.encode('ascii', 'replace').decode('ascii')
        print(safe_message)
    except Exception as e:
        # 如果还有其他错误，输出简化版本
        print(f"[LOG] {str(e)}")

def check_dependencies():
    """检查依赖是否已安装"""
    safe_print("[检查] 检查依赖包...")
    
    required_packages = ['flask', 'requests', 'flask-cors', 'psutil']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        safe_print(f"[错误] 缺少依赖包: {', '.join(missing_packages)}")
        safe_print("[安装] 正在安装依赖包...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + missing_packages)
            safe_print("[成功] 依赖包安装完成")
        except subprocess.CalledProcessError:
            safe_print("[错误] 依赖包安装失败，请手动运行: pip install -r requirements.txt")
            return False
    else:
        safe_print("[成功] 所有依赖包已安装")
    
    return True

def start_server(script_name, server_name, port):
    """启动服务器"""
    safe_print(f"[启动] 启动 {server_name} (端口 {port})...")
    
    try:
        process = subprocess.Popen(
            [sys.executable, script_name],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
            bufsize=1
        )
        server_processes.append((process, server_name))
        
        # 等待服务器启动
        time.sleep(2)
        
        if process.poll() is None:
            safe_print(f"[成功] {server_name} 启动成功")
            return True
        else:
            stdout, stderr = process.communicate()
            safe_print(f"[错误] {server_name} 启动失败")
            if stderr:
                safe_print(f"错误信息: {stderr}")
            return False
    except Exception as e:
        safe_print(f"[错误] 启动 {server_name} 时发生错误: {e}")
        return False

def monitor_server_output(process, server_name):
    """监控服务器输出"""
    try:
        for line in iter(process.stdout.readline, ''):
            if line:
                print(f"[{server_name}] {line.strip()}")
    except:
        pass

def signal_handler(signum, frame):
    """信号处理器，用于优雅关闭"""
    safe_print("\n[停止] 接收到关闭信号，正在关闭服务器...")
    stop_all_servers()
    sys.exit(0)

def stop_all_servers():
    """停止所有服务器"""
    for process, server_name in server_processes:
        if process.poll() is None:
            safe_print(f"[停止] 关闭 {server_name}...")
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
    server_processes.clear()

def check_server_health():
    """检查服务器健康状态"""
    import requests
    
    servers = [
        ("本地历史服务器", "http://127.0.0.1:5101/health"),
        ("OpenAI兼容服务器", "http://127.0.0.1:5100")
    ]
    
    safe_print("\n[健康] 检查服务器健康状态...")
    all_healthy = True

    for server_name, url in servers:
        try:
            response = requests.get(url, timeout=3)
            if response.status_code == 200:
                safe_print(f"[成功] {server_name}: 健康")
            else:
                safe_print(f"[警告] {server_name}: 响应异常 (HTTP {response.status_code})")
                all_healthy = False
        except requests.exceptions.RequestException:
            safe_print(f"[错误] {server_name}: 无法连接")
            all_healthy = False
    
    return all_healthy

def show_usage_instructions():
    """显示使用说明"""
    safe_print("\n" + "="*70)
    safe_print("[指南] GeminiBridge 使用指南")
    safe_print("="*70)
    safe_print("[步骤1] 安装油猴脚本:")
    safe_print("   - 确保浏览器已安装 Tampermonkey 扩展")
    safe_print("   - 安装以下脚本文件:")
    safe_print("     • TampermonkeyScript/automator.js")
    safe_print("     • TampermonkeyScript/historyforger.js")
    safe_print("     • TampermonkeyScript/modelfetcher.js")

    safe_print("\n[步骤2] 打开AI Studio:")
    safe_print("   - 访问 https://aistudio.google.com")
    safe_print("   - 打开一个现有的对话页面（不要是空白对话）")

    safe_print("\n[步骤3] 测试连接:")
    safe_print("   - 油猴脚本会自动连接到本地服务器")
    safe_print("   - 查看浏览器控制台确认脚本正常运行")

    safe_print("\n[步骤4] 性能监控:")
    safe_print("   - 运行: python performance_comparison.py")
    safe_print("   - 查看实时性能: http://127.0.0.1:5101/stats")
    safe_print("   - 健康检查: http://127.0.0.1:5101/health")

    safe_print("\n[步骤5] 负载测试:")
    safe_print("   - 运行: python performance_test.py")

    safe_print("\n[地址] 服务器地址:")
    safe_print("   - 本地历史服务器: http://127.0.0.1:5101")
    safe_print("   - OpenAI兼容服务器: http://127.0.0.1:5100")
    safe_print("="*70)

def main():
    """主函数"""
    safe_print("[启动] GeminiBridge 一键启动工具")
    safe_print("版本: v6.2 (Performance Optimized)")
    safe_print("-" * 50)
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 检查依赖
    if not check_dependencies():
        return 1
    
    # 检查脚本文件是否存在
    required_files = ['local_history_server.py', 'openai_compatible_server.py']
    for file in required_files:
        if not Path(file).exists():
            safe_print(f"[错误] 找不到文件: {file}")
            return 1

    safe_print("[成功] 所有必需文件已找到")
    
    # 启动服务器
    servers_to_start = [
        ('local_history_server.py', '本地历史服务器', 5101),
        ('openai_compatible_server.py', 'OpenAI兼容服务器', 5100)
    ]
    
    success_count = 0
    for script, name, port in servers_to_start:
        if start_server(script, name, port):
            success_count += 1
        else:
            safe_print(f"[错误] {name} 启动失败")

    if success_count == 0:
        safe_print("[错误] 所有服务器启动失败")
        return 1
    elif success_count < len(servers_to_start):
        safe_print("[警告] 部分服务器启动失败")
    else:
        safe_print("[成功] 所有服务器启动成功")
    
    # 等待服务器完全启动
    safe_print("\n[等待] 等待服务器完全启动...")
    time.sleep(3)

    # 健康检查
    if check_server_health():
        safe_print("[成功] 所有服务器运行正常")
    else:
        safe_print("[警告] 部分服务器可能存在问题")
    
    # 显示使用说明
    show_usage_instructions()
    
    # 启动输出监控线程
    for process, server_name in server_processes:
        if process.poll() is None:
            monitor_thread = threading.Thread(
                target=monitor_server_output, 
                args=(process, server_name),
                daemon=True
            )
            monitor_thread.start()
    
    safe_print(f"\n[完成] GeminiBridge 已启动完成！")
    safe_print("[提示] 提示: 按 Ctrl+C 停止所有服务器")
    safe_print("[统计] 运行 'python performance_comparison.py' 查看性能状态")
    
    # 保持主进程运行
    try:
        while True:
            # 检查进程是否还在运行
            running_count = 0
            for process, server_name in server_processes:
                if process.poll() is None:
                    running_count += 1
                else:
                    safe_print(f"[警告] {server_name} 已停止运行")

            if running_count == 0:
                safe_print("[错误] 所有服务器都已停止")
                break
            
            time.sleep(5)
    except KeyboardInterrupt:
        pass
    finally:
        stop_all_servers()
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
