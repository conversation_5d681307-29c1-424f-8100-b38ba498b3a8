# -*- coding: utf-8 -*-
# local_history_server.py

import sys
import io
from flask import Flask, request, jsonify
from queue import Queue, Empty
import logging
import uuid
import threading
import time
from collections import OrderedDict
import psutil
import os
from functools import wraps

# --- 配置 ---
log = logging.getLogger('werkzeug')
log.setLevel(logging.ERROR)
app = Flask(__name__)

# --- 安全打印函数 ---
def safe_print(message):
    """安全打印函数，处理Unicode编码问题"""
    try:
        print(message)
    except UnicodeEncodeError:
        # 如果遇到编码错误，将Unicode字符替换为ASCII等价物
        safe_message = message.encode('ascii', 'replace').decode('ascii')
        print(safe_message)
    except Exception as e:
        # 如果还有其他错误，输出简化版本
        print(f"[LOG] {str(e)}")

# --- 表情符号映射 ---
EMOJI_MAP = {
    '🧹': '[清理]',
    '🚀': '[启动]',
    '📊': '[统计]',
    '✅': '[成功]',
    '❌': '[错误]',
    '⚠️': '[警告]',
    '💾': '[内存]',
    '🔥': '[CPU]',
    '🧵': '[线程]',
    '📥': '[接收]',
    '💬': '[对话]',
    '🔧': '[工具]',
    '🤖': '[模型]',
    '📋': '[队列]',
    '📈': '[增长]',
    '📉': '[减少]',
    '🎯': '[目标]',
    '♻️': '[回收]',
    '🏥': '[健康]',
    '⏰': '[时间]',
    '💡': '[提示]'
}

def replace_emojis(text):
    """将Unicode表情符号替换为ASCII描述"""
    for emoji, replacement in EMOJI_MAP.items():
        text = text.replace(emoji, replacement)
    return text

# --- 性能优化配置 ---
TASK_TTL_SECONDS = 3600  # 任务结果保存1小时
MAX_CACHED_TASKS = 1000  # 最大缓存任务数
CLEANUP_INTERVAL = 300   # 清理间隔5分钟
MEMORY_STATS = {
    "total_tasks_created": 0,
    "total_tasks_cleaned": 0,
    "current_cached_tasks": 0,
    "last_cleanup_time": 0
}

# 性能监控统计
PERFORMANCE_STATS = {
    "server_start_time": time.time(),
    "total_requests": 0,
    "successful_requests": 0,
    "failed_requests": 0,
    "average_response_time": 0,
    "total_response_time": 0,
    "peak_memory_usage": 0,
    "current_connections": 0
}

class TaskResultManager:
    """带有TTL和LRU功能的任务结果管理器"""

    def __init__(self, max_size=MAX_CACHED_TASKS, ttl_seconds=TASK_TTL_SECONDS):
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        self.data = OrderedDict()  # 使用OrderedDict实现LRU
        self.lock = threading.RLock()
        self.start_cleanup_thread()

    def set(self, task_id, value):
        """设置任务结果"""
        with self.lock:
            current_time = time.time()
            # 如果任务已存在，先删除（用于更新）
            if task_id in self.data:
                del self.data[task_id]

            # 添加新任务
            self.data[task_id] = {
                "value": value,
                "created_time": current_time,
                "last_access_time": current_time
            }

            # 移到末尾（最近使用）
            self.data.move_to_end(task_id)

            # 检查是否超过最大容量
            if len(self.data) > self.max_size:
                # 删除最旧的任务
                oldest_task_id = next(iter(self.data))
                del self.data[oldest_task_id]
                MEMORY_STATS["total_tasks_cleaned"] += 1

            MEMORY_STATS["total_tasks_created"] += 1
            MEMORY_STATS["current_cached_tasks"] = len(self.data)

    def get(self, task_id):
        """获取任务结果"""
        with self.lock:
            if task_id not in self.data:
                return None

            current_time = time.time()
            task_data = self.data[task_id]

            # 检查是否过期
            if current_time - task_data["created_time"] > self.ttl_seconds:
                del self.data[task_id]
                MEMORY_STATS["total_tasks_cleaned"] += 1
                MEMORY_STATS["current_cached_tasks"] = len(self.data)
                return None

            # 更新访问时间并移到末尾
            task_data["last_access_time"] = current_time
            self.data.move_to_end(task_id)

            return task_data["value"]

    def cleanup_expired(self):
        """清理过期任务"""
        with self.lock:
            current_time = time.time()
            expired_tasks = []

            for task_id, task_data in self.data.items():
                if current_time - task_data["created_time"] > self.ttl_seconds:
                    expired_tasks.append(task_id)

            for task_id in expired_tasks:
                del self.data[task_id]
                MEMORY_STATS["total_tasks_cleaned"] += 1

            MEMORY_STATS["current_cached_tasks"] = len(self.data)
            MEMORY_STATS["last_cleanup_time"] = current_time

            if expired_tasks:
                safe_print(f"[清理] [Memory Cleanup] 清理了 {len(expired_tasks)} 个过期任务")

    def start_cleanup_thread(self):
        """启动清理线程"""
        def cleanup_worker():
            while True:
                time.sleep(CLEANUP_INTERVAL)
                self.cleanup_expired()

        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()
        safe_print(f"[清理] [Memory Manager] 自动清理线程已启动，清理间隔: {CLEANUP_INTERVAL}秒")

# --- 性能监控装饰器 ---
def monitor_performance(f):
    """性能监控装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        start_time = time.time()
        PERFORMANCE_STATS["total_requests"] += 1
        PERFORMANCE_STATS["current_connections"] += 1

        try:
            result = f(*args, **kwargs)
            PERFORMANCE_STATS["successful_requests"] += 1
            return result
        except Exception as e:
            PERFORMANCE_STATS["failed_requests"] += 1
            raise e
        finally:
            end_time = time.time()
            response_time = end_time - start_time
            PERFORMANCE_STATS["total_response_time"] += response_time
            PERFORMANCE_STATS["average_response_time"] = (
                PERFORMANCE_STATS["total_response_time"] / PERFORMANCE_STATS["total_requests"]
            )
            PERFORMANCE_STATS["current_connections"] -= 1

            # 更新峰值内存使用
            try:
                current_memory = psutil.Process(os.getpid()).memory_info().rss / 1024 / 1024  # MB
                if current_memory > PERFORMANCE_STATS["peak_memory_usage"]:
                    PERFORMANCE_STATS["peak_memory_usage"] = current_memory
            except:
                pass  # 如果psutil不可用，忽略内存监控

    return decorated_function

def get_system_stats():
    """获取系统性能统计"""
    try:
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        cpu_percent = process.cpu_percent()

        return {
            "memory_usage_mb": round(memory_info.rss / 1024 / 1024, 2),
            "memory_usage_percent": round(process.memory_percent(), 2),
            "cpu_usage_percent": round(cpu_percent, 2),
            "num_threads": process.num_threads(),
            "open_files": len(process.open_files()) if hasattr(process, 'open_files') else 0,
            "connections": len(process.connections()) if hasattr(process, 'connections') else 0
        }
    except:
        return {
            "memory_usage_mb": 0,
            "memory_usage_percent": 0,
            "cpu_usage_percent": 0,
            "num_threads": 0,
            "open_files": 0,
            "connections": 0
        }

# --- 数据存储 ---
INJECTION_JOBS = Queue()
PROMPT_JOBS = Queue()
TOOL_RESULT_JOBS = Queue()
MODEL_FETCH_JOBS = Queue() # 【新】为获取模型列表创建的队列

# 使用优化的任务结果管理器替代原始字典
RESULTS = TaskResultManager()

# 【新】用于缓存从油猴脚本获取的模型数据
REPORTED_MODELS_CACHE = {
    "data": None,
    "timestamp": 0,
    "event": threading.Event() # 用于通知等待方数据已到达
}


# --- API 端点 ---

@app.route('/')
def index():
    return "历史编辑代理服务器 v6.1 (Memory Optimized) 正在运行。"

@app.route('/stats', methods=['GET'])
@monitor_performance
def get_stats():
    """获取服务器性能统计信息"""
    current_time = time.time()
    uptime_seconds = current_time - PERFORMANCE_STATS["server_start_time"]

    stats = {
        # 内存管理统计
        "memory_management": MEMORY_STATS.copy(),

        # 性能统计
        "performance": PERFORMANCE_STATS.copy(),
        "performance_extended": {
            "uptime_seconds": round(uptime_seconds, 2),
            "uptime_hours": round(uptime_seconds / 3600, 2),
            "requests_per_second": round(PERFORMANCE_STATS["total_requests"] / uptime_seconds, 2) if uptime_seconds > 0 else 0,
            "success_rate_percent": round((PERFORMANCE_STATS["successful_requests"] / PERFORMANCE_STATS["total_requests"]) * 100, 2) if PERFORMANCE_STATS["total_requests"] > 0 else 0,
            "average_response_time_ms": round(PERFORMANCE_STATS["average_response_time"] * 1000, 2)
        },

        # 系统资源统计
        "system": get_system_stats(),

        # 队列状态
        "queues": {
            "injection_jobs": INJECTION_JOBS.qsize(),
            "prompt_jobs": PROMPT_JOBS.qsize(),
            "tool_result_jobs": TOOL_RESULT_JOBS.qsize(),
            "model_fetch_jobs": MODEL_FETCH_JOBS.qsize()
        },

        # 配置信息
        "config": {
            "task_ttl_seconds": TASK_TTL_SECONDS,
            "max_cached_tasks": MAX_CACHED_TASKS,
            "cleanup_interval_seconds": CLEANUP_INTERVAL
        }
    }

    return jsonify(stats)

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查端点"""
    current_time = time.time()
    uptime = current_time - PERFORMANCE_STATS["server_start_time"]

    # 计算健康状态
    health_status = "healthy"
    warnings = []

    # 检查内存使用
    system_stats = get_system_stats()
    if system_stats["memory_usage_percent"] > 80:
        health_status = "warning"
        warnings.append(f"High memory usage: {system_stats['memory_usage_percent']}%")

    # 检查CPU使用
    if system_stats["cpu_usage_percent"] > 80:
        health_status = "warning"
        warnings.append(f"High CPU usage: {system_stats['cpu_usage_percent']}%")

    # 检查错误率
    if PERFORMANCE_STATS["total_requests"] > 0:
        error_rate = (PERFORMANCE_STATS["failed_requests"] / PERFORMANCE_STATS["total_requests"]) * 100
        if error_rate > 10:
            health_status = "warning"
            warnings.append(f"High error rate: {error_rate:.1f}%")

    # 检查响应时间
    if PERFORMANCE_STATS["average_response_time"] > 1.0:  # 1秒
        health_status = "warning"
        warnings.append(f"Slow response time: {PERFORMANCE_STATS['average_response_time']:.2f}s")

    return jsonify({
        "status": health_status,
        "uptime_seconds": round(uptime, 2),
        "memory_usage_mb": system_stats["memory_usage_mb"],
        "cpu_usage_percent": system_stats["cpu_usage_percent"],
        "total_requests": PERFORMANCE_STATS["total_requests"],
        "error_rate_percent": round((PERFORMANCE_STATS["failed_requests"] / PERFORMANCE_STATS["total_requests"]) * 100, 2) if PERFORMANCE_STATS["total_requests"] > 0 else 0,
        "average_response_time_ms": round(PERFORMANCE_STATS["average_response_time"] * 1000, 2),
        "cached_tasks": MEMORY_STATS["current_cached_tasks"],
        "warnings": warnings,
        "timestamp": current_time
    })

# --- 注入 API (无变化) ---
@app.route('/submit_injection_job', methods=['POST'])
def submit_injection_job():
    job_data = request.json
    INJECTION_JOBS.put(job_data)
    safe_print(f"[成功] 已接收到新的【注入任务】。注入队列现有任务: {INJECTION_JOBS.qsize()}。")
    return jsonify({"status": "success", "message": "Injection job submitted"}), 200

@app.route('/get_injection_job', methods=['GET'])
def get_injection_job():
    try:
        job = INJECTION_JOBS.get_nowait()
        safe_print(f"[启动] History Forger 已取走注入任务。队列剩余: {INJECTION_JOBS.qsize()}。")
        return jsonify({"status": "success", "job": job}), 200
    except Empty:
        return jsonify({"status": "empty"}), 200

# --- 交互式对话 API (升级以支持流式传输) ---

@app.route('/submit_prompt', methods=['POST'])
@monitor_performance
def submit_prompt():
    data = request.json
    if not data or 'prompt' not in data:
        return jsonify({"status": "error", "message": "需要 'prompt' 字段。"}), 400
    
    task_id = str(uuid.uuid4())
    job = {"task_id": task_id, "prompt": data['prompt']}
    PROMPT_JOBS.put(job)
    # 为新任务初始化结果存储，包括一个专用的流队列
    RESULTS.set(task_id, {
        "status": "pending",
        "stream_queue": Queue(),
        "full_response": None
    })
    safe_print(f"[成功] 已接收到新的【对话任务】(ID: {task_id[:8]})。对话队列现有任务: {PROMPT_JOBS.qsize()}。")
    return jsonify({"status": "success", "task_id": task_id}), 200

@app.route('/get_prompt_job', methods=['GET'])
def get_prompt_job():
    try:
        job = PROMPT_JOBS.get_nowait()
        safe_print(f"[启动] Automator 已取走对话任务 (ID: {job['task_id'][:8]})。队列剩余: {PROMPT_JOBS.qsize()}。")
        return jsonify({"status": "success", "job": job}), 200
    except Empty:
        return jsonify({"status": "empty"}), 200

# --- 【【【新】】】流式数据 API ---

@app.route('/stream_chunk', methods=['POST'])
@monitor_performance
def stream_chunk():
    """接收油猴脚本发送的流式数据块"""
    data = request.json
    task_id = data.get('task_id')
    chunk = data.get('chunk')
    
    # 【【【调试日志】】】
    safe_print(f"\n--- [接收] [Local Server] 收到来自 Automator 的数据块 (Task ID: {task_id[:8]}) ---")
    safe_print(str(chunk))
    safe_print("--------------------------------------------------------------------")
    
    task_result = RESULTS.get(task_id)
    if task_result:
        # 将数据块（或结束信号）放入对应任务的队列中
        task_result['stream_queue'].put(chunk)
        return jsonify({"status": "success"}), 200
    return jsonify({"status": "error", "message": "无效的任务 ID"}), 404

@app.route('/get_chunk/<task_id>', methods=['GET'])
@monitor_performance
def get_chunk(task_id):
    """Python 客户端从此端点轮询数据块"""
    task_result = RESULTS.get(task_id)
    if task_result:
        try:
            # 非阻塞地从队列中获取数据
            chunk = task_result['stream_queue'].get_nowait()
            # 【【【调试日志】】】
            safe_print(f"\n--- [发送] [Local Server] API 网关已取走数据块 (Task ID: {task_id[:8]}) ---")
            safe_print(str(chunk))
            safe_print("------------------------------------------------------------------")
            return jsonify({"status": "ok", "chunk": chunk}), 200
        except Empty:
            # 如果队列为空，检查任务是否已完成
            if task_result['status'] in ['completed', 'failed']:
                return jsonify({"status": "done"}), 200
            else:
                return jsonify({"status": "empty"}), 200
    return jsonify({"status": "not_found"}), 404
    
@app.route('/report_result', methods=['POST'])
def report_result():
    """当油猴脚本确认整个对话结束后，调用此接口来最终确定任务状态"""
    data = request.json
    task_id = data.get('task_id')
    task_result = RESULTS.get(task_id)
    if task_id and task_result:
        task_result['status'] = data.get('status', 'completed')
        task_result['full_response'] = data.get('content', '') # 存储最终的完整响应以供调试
        # 更新任务结果
        RESULTS.set(task_id, task_result)
        safe_print(f"[完成] 任务 {task_id[:8]} 已完成。状态: {task_result['status']}。")
        return jsonify({"status": "success"}), 200
    return jsonify({"status": "error", "message": "无效的任务 ID。"}), 404

# --- 【【【新】】】工具函数结果 API ---

@app.route('/submit_tool_result', methods=['POST'])
def submit_tool_result():
    """接收来自 OpenAI 网关的工具函数执行结果，并为响应流准备好存储空间"""
    data = request.json
    if not data or 'task_id' not in data or 'result' not in data:
        return jsonify({"status": "error", "message": "需要 'task_id' 和 'result' 字段。"}), 400
    
    task_id = data['task_id']
    job = {"task_id": task_id, "result": data['result']}
    TOOL_RESULT_JOBS.put(job)

    # 【【【核心修复】】】为这个新任务初始化结果存储，否则后续的流数据将无处安放
    RESULTS.set(task_id, {
        "status": "pending",
        "stream_queue": Queue(),
        "full_response": None
    })
    
    safe_print(f"[成功] 已接收到新的【工具返回任务】(ID: {task_id[:8]}) 并已为其准备好流接收队列。工具队列现有任务: {TOOL_RESULT_JOBS.qsize()}。")
    return jsonify({"status": "success"}), 200

@app.route('/get_tool_result_job', methods=['GET'])
def get_tool_result_job():
    """供 Automator 油猴脚本获取工具函数返回任务"""
    try:
        job = TOOL_RESULT_JOBS.get_nowait()
        safe_print(f"[启动] Automator 已取走工具返回任务 (ID: {job['task_id'][:8]})。队列剩余: {TOOL_RESULT_JOBS.qsize()}。")
        return jsonify({"status": "success", "job": job}), 200
    except Empty:
        return jsonify({"status": "empty"}), 200

# --- 【【【新】】】模型获取 API ---

@app.route('/submit_model_fetch_job', methods=['POST'])
def submit_model_fetch_job():
    """由 OpenAI 网关调用，创建一个“获取模型列表”的任务"""
    if not MODEL_FETCH_JOBS.empty():
        return jsonify({"status": "success", "message": "A fetch job is already pending."}), 200
    
    task_id = str(uuid.uuid4())
    job = {"task_id": task_id, "type": "FETCH_MODELS"}
    MODEL_FETCH_JOBS.put(job)
    
    # 重置事件，以便新的请求可以等待
    REPORTED_MODELS_CACHE['event'].clear()
    REPORTED_MODELS_CACHE['data'] = None

    safe_print(f"[成功] 已接收到新的【模型获取任务】(ID: {task_id[:8]})。")
    return jsonify({"status": "success", "task_id": task_id})

@app.route('/get_model_fetch_job', methods=['GET'])
def get_model_fetch_job():
    """由 Model Fetcher 油猴脚本轮询，以检查是否有待处理的获取任务"""
    try:
        job = MODEL_FETCH_JOBS.queue[0] # 查看任务但不取出
        return jsonify({"status": "success", "job": job}), 200
    except IndexError:
        return jsonify({"status": "empty"}), 200

@app.route('/acknowledge_model_fetch_job', methods=['POST'])
def acknowledge_model_fetch_job():
    """Model Fetcher 在收到任务并准备刷新页面前调用此接口，以从队列中安全地移除任务"""
    try:
        job = MODEL_FETCH_JOBS.get_nowait()
        safe_print(f"[启动] Model Fetcher 已确认并取走模型获取任务 (ID: {job['task_id'][:8]})。")
        return jsonify({"status": "success"}), 200
    except Empty:
        return jsonify({"status": "error", "message": "No job to acknowledge."}), 400


@app.route('/report_models', methods=['POST'])
def report_models():
    """由 Model Fetcher 油猴脚本调用，以发送拦截到的原始模型数据"""
    data = request.json
    models_json = data.get('models_json')
    if models_json:
        REPORTED_MODELS_CACHE['data'] = models_json
        REPORTED_MODELS_CACHE['timestamp'] = uuid.uuid4().int # 使用UUID确保时间戳唯一
        REPORTED_MODELS_CACHE['event'].set() # 通知所有等待方，数据已到达
        safe_print(f"[完成] 成功接收并缓存了新的模型列表数据。")
        return jsonify({"status": "success"}), 200
    return jsonify({"status": "error", "message": "需要 'models_json' 字段。"}), 400

@app.route('/get_reported_models', methods=['GET'])
def get_reported_models():
    """由 OpenAI 网关调用，以获取缓存的模型数据。如果数据不存在，将等待。"""
    # 检查是否有数据，或者等待事件被设置
    wait_result = REPORTED_MODELS_CACHE['event'].wait(timeout=60) # 等待最多60秒
    if not wait_result:
        return jsonify({"status": "error", "message": "等待模型数据超时 (60 秒)。"}), 408

    if REPORTED_MODELS_CACHE['data']:
        return jsonify({
            "status": "success",
            "data": REPORTED_MODELS_CACHE['data'],
            "timestamp": REPORTED_MODELS_CACHE['timestamp']
        }), 200
    else:
        # 这种情况理论上不应该发生，因为事件被设置了
        return jsonify({"status": "error", "message": "数据获取失败，即使事件已触发。"}), 500


if __name__ == '__main__':
    safe_print("======================================================================")
    safe_print("  历史编辑代理服务器 v6.2 (Performance Monitoring)")
    safe_print("  [启动] 新功能: 智能内存管理 - TTL机制 + LRU缓存")
    safe_print("  [统计] 性能监控: 实时性能指标收集和监控")
    safe_print("  [健康] 健康检查: /health 端点监控服务器状态")
    safe_print("  - /submit_injection_job, /get_injection_job (用于初始注入)")
    safe_print("  - /submit_prompt, /get_prompt_job (用于发起对话)")
    safe_print("  - /submit_tool_result, /get_tool_result_job (用于返回工具结果)")
    safe_print("  - /submit_model_fetch_job, /get_model_fetch_job (用于获取模型)")
    safe_print("  - /stream_chunk, /get_chunk (用于流式传输)")
    safe_print("  - /stats (详细性能统计信息)")
    safe_print("  - /health (健康检查和警告)")
    safe_print(f"  [清理] 自动清理: 每{CLEANUP_INTERVAL}秒清理过期任务")
    safe_print(f"  [内存] 缓存配置: 最大{MAX_CACHED_TASKS}个任务，TTL {TASK_TTL_SECONDS}秒")
    safe_print("  已在 http://127.0.0.1:5101 启动")
    safe_print("======================================================================")
    app.run(host='0.0.0.0', port=5101, threaded=True)