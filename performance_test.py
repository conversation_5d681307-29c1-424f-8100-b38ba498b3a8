#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能测试脚本 - 验证GeminiBridge优化效果
测试内存管理、轮询机制和性能监控功能
"""

import requests
import time
import json
import threading
from concurrent.futures import ThreadPoolExecutor
import statistics

# 测试配置
LOCAL_SERVER_URL = "http://127.0.0.1:5101"
TEST_DURATION = 60  # 测试持续时间（秒）
CONCURRENT_REQUESTS = 5  # 并发请求数

class PerformanceTest:
    def __init__(self):
        self.results = {
            "response_times": [],
            "success_count": 0,
            "error_count": 0,
            "memory_usage": [],
            "start_time": None,
            "end_time": None
        }
        self.running = False
    
    def test_server_health(self):
        """测试服务器健康状态"""
        try:
            response = requests.get(f"{LOCAL_SERVER_URL}/health", timeout=5)
            if response.status_code == 200:
                health_data = response.json()
                print(f"[成功] 服务器健康检查通过")
                print(f"   状态: {health_data.get('status', 'unknown')}")
                print(f"   内存使用: {health_data.get('memory_usage_mb', 0):.1f} MB")
                print(f"   CPU使用: {health_data.get('cpu_usage_percent', 0):.1f}%")
                return True
            else:
                print(f"[错误] 健康检查失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"[错误] 无法连接到服务器: {e}")
            return False
    
    def get_server_stats(self):
        """获取服务器统计信息"""
        try:
            response = requests.get(f"{LOCAL_SERVER_URL}/stats", timeout=5)
            if response.status_code == 200:
                return response.json()
            return None
        except:
            return None
    
    def simulate_task_creation(self, task_id):
        """模拟任务创建和处理"""
        start_time = time.time()
        try:
            # 模拟提交prompt任务
            payload = {"prompt": f"测试任务 {task_id} - {time.time()}"}
            response = requests.post(
                f"{LOCAL_SERVER_URL}/submit_prompt",
                json=payload,
                timeout=10
            )
            
            if response.status_code == 200:
                self.results["success_count"] += 1
            else:
                self.results["error_count"] += 1
            
            response_time = time.time() - start_time
            self.results["response_times"].append(response_time)
            
        except Exception as e:
            self.results["error_count"] += 1
            response_time = time.time() - start_time
            self.results["response_times"].append(response_time)
    
    def monitor_memory_usage(self):
        """监控内存使用情况"""
        while self.running:
            stats = self.get_server_stats()
            if stats and "system" in stats:
                memory_mb = stats["system"].get("memory_usage_mb", 0)
                self.results["memory_usage"].append(memory_mb)
            time.sleep(5)  # 每5秒检查一次
    
    def run_load_test(self):
        """运行负载测试"""
        print(f"[启动] 开始负载测试 - 持续时间: {TEST_DURATION}秒, 并发数: {CONCURRENT_REQUESTS}")
        
        self.running = True
        self.results["start_time"] = time.time()
        
        # 启动内存监控线程
        memory_thread = threading.Thread(target=self.monitor_memory_usage)
        memory_thread.daemon = True
        memory_thread.start()
        
        # 执行并发请求
        with ThreadPoolExecutor(max_workers=CONCURRENT_REQUESTS) as executor:
            task_id = 0
            end_time = time.time() + TEST_DURATION
            
            while time.time() < end_time:
                executor.submit(self.simulate_task_creation, task_id)
                task_id += 1
                time.sleep(0.1)  # 控制请求频率
        
        self.running = False
        self.results["end_time"] = time.time()
        
        print("[成功] 负载测试完成")
    
    def analyze_results(self):
        """分析测试结果"""
        if not self.results["response_times"]:
            print("[错误] 没有收集到测试数据")
            return
        
        total_requests = self.results["success_count"] + self.results["error_count"]
        success_rate = (self.results["success_count"] / total_requests) * 100 if total_requests > 0 else 0
        
        response_times = self.results["response_times"]
        avg_response_time = statistics.mean(response_times)
        median_response_time = statistics.median(response_times)
        p95_response_time = sorted(response_times)[int(len(response_times) * 0.95)] if response_times else 0
        
        print("\n" + "="*60)
        print("[统计] 性能测试结果分析")
        print("="*60)
        print(f"总请求数: {total_requests}")
        print(f"成功请求: {self.results['success_count']}")
        print(f"失败请求: {self.results['error_count']}")
        print(f"成功率: {success_rate:.1f}%")
        print(f"平均响应时间: {avg_response_time*1000:.1f} ms")
        print(f"中位数响应时间: {median_response_time*1000:.1f} ms")
        print(f"95%响应时间: {p95_response_time*1000:.1f} ms")
        
        if self.results["memory_usage"]:
            avg_memory = statistics.mean(self.results["memory_usage"])
            max_memory = max(self.results["memory_usage"])
            print(f"平均内存使用: {avg_memory:.1f} MB")
            print(f"峰值内存使用: {max_memory:.1f} MB")
        
        # 获取最终服务器统计
        final_stats = self.get_server_stats()
        if final_stats:
            print("\n[增长] 服务器性能统计:")
            perf = final_stats.get("performance", {})
            print(f"服务器总请求数: {perf.get('total_requests', 0)}")
            print(f"服务器成功率: {final_stats.get('performance_extended', {}).get('success_rate_percent', 0):.1f}%")
            print(f"服务器平均响应时间: {final_stats.get('performance_extended', {}).get('average_response_time_ms', 0):.1f} ms")
            
            memory_mgmt = final_stats.get("memory_management", {})
            print(f"缓存任务数: {memory_mgmt.get('current_cached_tasks', 0)}")
            print(f"已清理任务数: {memory_mgmt.get('total_tasks_cleaned', 0)}")
        
        print("="*60)

def main():
    """主函数"""
    print("[工具] GeminiBridge 性能测试工具")
    print("测试内容: 内存管理、轮询优化、性能监控")
    print("-" * 50)
    
    test = PerformanceTest()
    
    # 1. 健康检查
    print("[步骤1] 执行服务器健康检查...")
    if not test.test_server_health():
        print("[错误] 服务器健康检查失败，请确保服务器正在运行")
        return
    
    # 2. 获取初始统计
    print("\n[步骤2] 获取初始性能统计...")
    initial_stats = test.get_server_stats()
    if initial_stats:
        print(f"   初始缓存任务数: {initial_stats.get('memory_management', {}).get('current_cached_tasks', 0)}")
        print(f"   服务器运行时间: {initial_stats.get('performance_extended', {}).get('uptime_hours', 0):.1f} 小时")
    
    # 3. 执行负载测试
    print("\n[步骤3] 执行负载测试...")
    test.run_load_test()
    
    # 4. 分析结果
    print("\n[步骤4] 分析测试结果...")
    test.analyze_results()
    
    print("\n[成功] 性能测试完成！")

if __name__ == "__main__":
    main()
